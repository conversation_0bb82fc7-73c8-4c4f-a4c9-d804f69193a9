# 间距使用规范 (简化版)

## 核心原则

遵循8px网格系统，使用标准Tailwind间距类，保持简洁直观。

## 标准间距值

| 用途 | Tailwind类 | 像素值 | 使用场景 |
|------|------------|--------|----------|
| 紧密关联 | `mb-2` | 8px | 标题与内容 |
| 内容间距 | `mb-4` | 16px | 段落、列表项 |
| 组件间距 | `my-6` | 24px | 组件之间 |
| 区块间距 | `mt-8` | 32px | 主要区块 |

## 使用规范

### ✅ 推荐做法

```vue
<!-- 组件间距 -->
<div class="my-6">

<!-- 内容间距 -->
<p class="mb-4">

<!-- 标题间距 -->
<h2 class="mb-2">

<!-- 区块间距 -->
<section class="mt-8">
```

### ❌ 避免使用

```vue
<!-- 非标准间距 -->
<div class="my-5">   <!-- 20px，不符合8px网格 -->
<div class="mt-7">   <!-- 28px，不符合8px网格 -->
<div class="mb-3">   <!-- 12px，不符合8px网格 -->

<!-- 内联样式 -->
<div style="margin: 20px 0">
```

## CSS变量 (可选)

如需全局调整，可使用CSS变量：

```css
:root {
  --spacing-xs: 0.5rem;   /* 8px */
  --spacing-sm: 1rem;     /* 16px */
  --spacing-md: 1.5rem;   /* 24px */
  --spacing-lg: 2rem;     /* 32px */
}
```

## 工具类 (可选)

```css
.component-spacing { margin: var(--spacing-md) 0; }  /* 24px */
.content-spacing { margin-bottom: var(--spacing-sm); }  /* 16px */
```

## 常见组件间距

| 组件 | 推荐间距 | 说明 |
|------|----------|------|
| ThinkingIndicator | `my-6` | 保持现有24px |
| Pinwheel | `my-4` | 已修复为16px |
| Suggestions | `mt-6` | 已修复为24px |
| Messages | `mb-6` | 保持现有24px |

## 检查清单

开发时请确认：
- [ ] 使用标准Tailwind间距类
- [ ] 间距值符合8px网格 (8, 16, 24, 32px)
- [ ] 避免内联样式
- [ ] 保持视觉层次清晰

## 快速参考

```bash
# 查找非标准间距
grep -r "my-[135789]\|mt-[135679]\|mb-[135679]" modules/

# 标准间距类
mb-2  # 8px
mb-4  # 16px  
my-6  # 24px
mt-8  # 32px
```

---

**记住**: 简洁胜过复杂，一致胜过完美。优先使用标准Tailwind类，仅在必要时使用CSS变量。
