# Pinwheel Loading Animation Configurations

**Last Updated:** 2025-01-22  
**Library:** ldrs@1.1.7  
**Component:** `<l-pinwheel>`

## Overview

This document records all pinwheel loading animation configurations across the project. All pinwheel instances use the unified project theme color for visual consistency.

## Global Configuration

- **Library**: `ldrs@1.1.7`
- **Import Pattern**: `const { pinwheel } = await import("ldrs")`
- **Registration**: `pinwheel.register()`
- **Theme Color**: `oklch(0.5613 0.0924 238.72)` (project primary color)

## Current Pinwheel Instances

### 📍 Size 20 - Content Area Animations

#### 1. UnderstandingPhase.vue
```vue
<l-pinwheel
  v-if="step.status === 'running' && isAgentAnalyzing && !summary"
  size="20"
  speed="1.3"
  color="oklch(0.5613 0.0924 238.72)"
  class="mt-2"
/>
```
- **Location**: `modules/sentire/components/UnderstandingPhase.vue:316`
- **Context**: Bottom of running step content, aligned with step content area
- **Position**: Right side of progress line, at the bottom of step content
- **Display Condition**: `step.status === 'running' && isAgentAnalyzing && !summary`

#### 2. Sentire Index Page
```vue
<l-pinwheel
  v-if="(isAgentAnalyzing && summary) || waitingForSuggestion"
  size="20"
  speed="1.3"
  color="oklch(0.5613 0.0924 238.72)"
  class="my-2"
/>
```
- **Location**: `pages/sentire/index.vue:303`
- **Context**: Deep analysis phase, below toolbar
- **Position**: Main analysis page loading state
- **Display Condition**: `(isAgentAnalyzing && summary) || waitingForSuggestion`

#### 3. Monitor Messages
```vue
<l-pinwheel
  v-if="isProcessing || isPending"
  size="20"
  speed="1.3"
  color="oklch(0.5613 0.0924 238.72)"
  class="my-2"
/>
```
- **Location**: `modules/monitor/components/Messages.vue:40`
- **Context**: Chat message processing indicator
- **Position**: Bottom of message stream
- **Display Condition**: `isProcessing || isPending`

### 📍 Size 40 - Component Placeholder Animations

#### 4. CubeChart Loading
```vue
<l-pinwheel size="40" speed="0.9" color="oklch(0.5613 0.0924 238.72)" />
```
- **Location**: `modules/monitor/components/CubeChart.vue:184`
- **Context**: Chart data loading state
- **Position**: Center of chart container (`flex justify-center items-center h-full`)
- **Display Condition**: `loading`

#### 5. AssistantMessage Placeholder
```vue
<l-pinwheel size="40" speed="1" color="oklch(0.5613 0.0924 238.72)" />
```
- **Location**: `modules/monitor/components/AssistantMessage.vue:83`
- **Context**: Assistant message card loading placeholder
- **Position**: Center of placeholder container (`w-96 h-64 flex items-center justify-center`)
- **Display Condition**: Tool call result is empty

## Size Distribution Summary

| **Size** | **Count** | **Usage Context** | **Files** |
|----------|-----------|-------------------|-----------|
| `20` | 3 | Content area animations | UnderstandingPhase, Sentire Index, Messages |
| `40` | 2 | Component placeholders | CubeChart, AssistantMessage |

## Speed Configuration

- **1.3**: Standard speed for content area animations (3 instances)
- **0.9**: Slower speed for chart loading (1 instance)
- **1.0**: Default speed for message placeholders (1 instance)

## Design Principles

### Size Guidelines
- **Size 20**: Used for inline content area animations that should not dominate the interface
- **Size 40**: Used for component placeholders where the animation is the primary visual element

### Position Guidelines
- **Content Area**: Positioned at the bottom of content sections, aligned with text content
- **Placeholder**: Centered within the container, serving as the main visual indicator

### Color Consistency
- All instances use the unified theme color: `oklch(0.5613 0.0924 238.72)`
- No custom colors or variations to maintain brand consistency

## Import and Registration Pattern

All components follow the same pattern:

```javascript
onMounted(async () => {
  const { pinwheel } = await import("ldrs");
  pinwheel.register();
});
```

## Related Documentation

- [Loading Animation Standardization Changelog](../changelog/2025-01-22-ldrs-loading-animation-standardization.md)
- [LDRS Library Documentation](https://uiball.com/ldrs/)
- [UI Consistency Standards](./ui-consistency-standards.md)

---

**Maintained by:** Development Team  
**Review Required:** When adding new pinwheel instances
