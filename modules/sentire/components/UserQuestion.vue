<script setup lang="ts">
import { slugify } from "@/utils";
import { ref, onMounted } from "vue";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";

defineProps<{
  value: string;
}>();

const isSticky = ref(false);
const elementRef = ref<HTMLElement>();

onMounted(() => {
  if (elementRef.value) {
    const observer = new IntersectionObserver(
      ([entry]) => {
        isSticky.value = entry.intersectionRatio < 1;
      },
      {
        threshold: [1],
        rootMargin: "-1px 0px 0px 0px",
      }
    );

    observer.observe(elementRef.value);
  }
});
</script>

<template>
  <div
    ref="elementRef"
    class="flex items-center gap-4 mb-8 sticky top-0 z-20 h-16 leading-16 bg-white"
  >
    <HoverCard v-if="isSticky">
      <HoverCardTrigger as-child>
        <div
          :id="slugify(value)"
          class="pl-4 text-base leading-relaxed text-default-text font-semibold relative rounded-border-left truncate"
        >
          {{ value }}
        </div>
      </HoverCardTrigger>
      <HoverCardContent side="right">
        <p>{{ value }}</p>
      </HoverCardContent>
    </HoverCard>
    <div
      v-else
      :id="slugify(value)"
      class="pl-4 text-xl md:text-2xl lg:text-3xl leading-snug text-default-text font-semibold relative rounded-border-left"
    >
      {{ value }}
    </div>
  </div>
</template>
<style scoped>
.rounded-border-left {
  position: relative;
}

.rounded-border-left::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: #3a7ca5;
  border-radius: 4px;
}
</style>
